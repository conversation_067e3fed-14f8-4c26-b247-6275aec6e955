<?xml version="1.0" encoding="utf-8"?>
<network-security-config>
    <!-- Allow cleartext traffic for all local network ranges -->
    <domain-config cleartextTrafficPermitted="true">
        <!-- Emulator and localhost -->
        <domain includeSubdomains="true">********</domain>
        <domain includeSubdomains="true">localhost</domain>
        <domain includeSubdomains="true">127.0.0.1</domain>

        <!-- Common router IPs -->
        <domain includeSubdomains="true">***********</domain>
        <domain includeSubdomains="true">***********</domain>
        <domain includeSubdomains="true">************</domain>
        <domain includeSubdomains="true">********</domain>

        <!-- Specific server IPs -->
        <domain includeSubdomains="true">**************</domain>
        <domain includeSubdomains="true">***********</domain>

        <!-- mDNS hostname -->
        <domain includeSubdomains="true">restaurant.local</domain>

        <!-- All private IP ranges -->
        <domain includeSubdomains="true">***********</domain>
        <domain includeSubdomains="true">***********</domain>
        <domain includeSubdomains="true">************</domain>
        <domain includeSubdomains="true">***********</domain>
        <domain includeSubdomains="true">10.0.0.0</domain>
    </domain-config>

    <!-- Fallback: Allow cleartext for all private IP ranges -->
    <base-config cleartextTrafficPermitted="true">
        <trust-anchors>
            <certificates src="system"/>
        </trust-anchors>
    </base-config>
</network-security-config>
