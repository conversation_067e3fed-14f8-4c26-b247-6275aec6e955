<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <!-- Internet permission for API calls -->
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />

    <!-- mDNS/Network Service Discovery permissions -->
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.CHANGE_WIFI_MULTICAST_STATE" />

    <!-- Additional network permissions for local discovery -->
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />

    <!-- Camera permission for QR code scanning -->
    <uses-permission android:name="android.permission.CAMERA" />

    <!-- Location permissions for WiFi/Bluetooth proximity -->
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />

    <!-- Bluetooth permissions -->
    <uses-permission android:name="android.permission.BLUETOOTH" />
    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />
    <uses-permission android:name="android.permission.BLUETOOTH_SCAN" />

    <!-- WiFi permissions -->
    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />

    <!-- NFC permission -->
    <uses-permission android:name="android.permission.NFC" />

    <!-- Camera feature -->
    <uses-feature
        android:name="android.hardware.camera"
        android:required="true" />

    <!-- NFC feature -->
    <uses-feature
        android:name="android.hardware.nfc"
        android:required="false" />

    <!-- Bluetooth feature -->
    <uses-feature
        android:name="android.hardware.bluetooth"
        android:required="false" />

    <application
        android:name=".SubscriberApplication"
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@android:drawable/ic_menu_camera"
        android:label="@string/app_name"
        android:roundIcon="@android:drawable/ic_menu_camera"
        android:supportsRtl="true"
        android:theme="@style/Theme.SubscriberApp"
        android:usesCleartextTraffic="true"
        android:networkSecurityConfig="@xml/network_security_config"
        tools:targetApi="31">

        <!-- Main Activity -->
        <activity
            android:name=".ui.MainActivity"
            android:exported="true"
            android:theme="@style/Theme.SubscriberApp">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>

            <!-- Deep link for QR code check-in -->
            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <data android:scheme="ams" android:host="checkin" />
            </intent-filter>
        </activity>

        <!-- Debug Activity -->
        <activity
            android:name=".ui.debug.DebugActivity"
            android:exported="false"
            android:theme="@style/Theme.SubscriberApp" />

        <!-- NFC Intent Filter -->
        <activity
            android:name=".ui.checkin.NfcCheckInActivity"
            android:exported="true"
            android:launchMode="singleTop">
            <intent-filter>
                <action android:name="android.nfc.action.NDEF_DISCOVERED" />
                <category android:name="android.intent.category.DEFAULT" />
                <data android:mimeType="text/plain" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.nfc.action.TAG_DISCOVERED" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>

        <!-- Background Services -->
        <service
            android:name=".services.BluetoothService"
            android:enabled="true"
            android:exported="false" />

        <service
            android:name=".services.LocationService"
            android:enabled="true"
            android:exported="false"
            android:foregroundServiceType="location" />

    </application>

</manifest>
