<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res/auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fillViewport="true"
    android:background="@color/background_light"
    tools:context=".ui.SessionDetailsFragment">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- Session Info Card -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:background="@drawable/stats_card_background"
            android:padding="20dp"
            android:layout_marginBottom="20dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_marginBottom="16dp">

                <ImageView
                    android:layout_width="48dp"
                    android:layout_height="48dp"
                    android:src="@drawable/ic_today"
                    android:background="@drawable/circle_gradient_blue"
                    android:padding="12dp"
                    android:layout_marginEnd="16dp" />

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/tvSessionName"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Session Name"
                        android:textSize="20sp"
                        android:textStyle="bold"
                        android:textColor="@color/text_primary" />

                    <TextView
                        android:id="@+id/tvSessionId"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Session ID: 12345"
                        android:textSize="14sp"
                        android:textColor="@color/text_secondary"
                        android:layout_marginTop="4dp" />

                </LinearLayout>

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical"
                    android:gravity="center">

                    <TextView
                        android:id="@+id/tvTotalAttendees"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="5 Attendees"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:textColor="@color/primary_blue" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Total"
                        android:textSize="12sp"
                        android:textColor="@color/text_secondary" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical"
                    android:gravity="center">

                    <TextView
                        android:id="@+id/tvSessionDate"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Dec 15, 2024"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:textColor="@color/success_green" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Date"
                        android:textSize="12sp"
                        android:textColor="@color/text_secondary" />

                </LinearLayout>

            </LinearLayout>

        </LinearLayout>

        <!-- Attendees List Card -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:background="@drawable/stats_card_background"
            android:padding="20dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_marginBottom="16dp">

                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:src="@drawable/ic_person"
                    android:layout_marginEnd="8dp" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Attendees"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="@color/text_primary" />

            </LinearLayout>

            <!-- Attendees RecyclerView -->
            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recyclerAttendees"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:nestedScrollingEnabled="false" />

            <!-- No Attendees State -->
            <TextView
                android:id="@+id/tvNoAttendees"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="No attendees yet"
                android:textAlignment="center"
                android:textColor="@color/text_secondary"
                android:textSize="16sp"
                android:padding="40dp"
                android:visibility="gone" />

        </LinearLayout>

    </LinearLayout>

</ScrollView>
