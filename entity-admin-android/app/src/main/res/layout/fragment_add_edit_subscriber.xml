<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="16dp">

    <EditText
        android:id="@+id/editTextSubscriberName"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:hint="Subscriber Name"
        android:inputType="textPersonName"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginTop="16dp"/>

    <EditText
        android:id="@+id/editTextSubscriberEmail"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:hint="Subscriber Email"
        android:inputType="textEmailAddress"
        app:layout_constraintTop_toBottomOf="@id/editTextSubscriberName"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginTop="16dp"/>

    <EditText
        android:id="@+id/editTextNfcCardUid"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:hint="NFC Card UID (Optional)"
        android:inputType="text"
        app:layout_constraintTop_toBottomOf="@id/editTextSubscriberEmail"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginTop="16dp"/>

    <Button
        android:id="@+id/buttonSaveSubscriber"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Save Subscriber"
        app:layout_constraintTop_toBottomOf="@id/editTextNfcCardUid"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginTop="32dp"/>

    <ProgressBar
        android:id="@+id/progressBarAddEditSubscriber"
        style="?android:attr/progressBarStyle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        tools:visibility="visible"/>

</androidx.constraintlayout.widget.ConstraintLayout>
