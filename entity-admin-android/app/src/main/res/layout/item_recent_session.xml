<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:padding="12dp"
    android:background="?attr/selectableItemBackground"
    android:gravity="center_vertical">

    <!-- Session Icon -->
    <LinearLayout
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:background="@drawable/circle_gradient_blue"
        android:gravity="center"
        android:layout_marginEnd="12dp">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="S"
            android:textSize="16sp"
            android:textStyle="bold"
            android:textColor="@color/text_on_primary" />

    </LinearLayout>

    <!-- Session Info -->
    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tvSessionName"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Session Name"
            android:textSize="14sp"
            android:textStyle="bold"
            android:textColor="@color/text_primary" />

        <TextView
            android:id="@+id/tvSessionId"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="ID: 12345"
            android:textSize="12sp"
            android:textColor="@color/text_secondary"
            android:layout_marginTop="2dp" />

    </LinearLayout>

    <!-- Status Badge -->
    <TextView
        android:id="@+id/tvSessionStatus"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Active"
        android:textSize="10sp"
        android:textColor="@color/text_on_primary"
        android:background="@drawable/status_active_background"
        android:paddingHorizontal="8dp"
        android:paddingVertical="4dp" />

</LinearLayout>
