<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/login_background"
    android:fillViewport="true">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="24dp">

        <!-- Logo and Title Section -->
        <ImageView
            android:id="@+id/logoImageView"
            android:layout_width="100dp"
            android:layout_height="100dp"
            android:layout_marginTop="48dp"
            android:src="@drawable/ic_business_center"
            android:tint="@color/text_on_primary"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/titleTextView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            android:text="Entity Admin"
            android:textColor="@color/text_on_primary"
            android:textSize="32sp"
            android:textStyle="bold"
            android:fontFamily="sans-serif-medium"
            android:letterSpacing="0.02"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/logoImageView" />

        <TextView
            android:id="@+id/subtitleTextView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:text="Attendance Management System"
            android:textColor="@color/text_on_primary"
            android:textSize="16sp"
            android:alpha="0.9"
            android:fontFamily="sans-serif"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/titleTextView" />

        <!-- Login Card -->
        <com.google.android.material.card.MaterialCardView
            android:id="@+id/loginCard"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="56dp"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="16dp"
            app:cardCornerRadius="20dp"
            app:cardElevation="12dp"
            app:cardBackgroundColor="@color/login_card_background"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/subtitleTextView">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="32dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="32dp"
                    android:text="Welcome Back"
                    android:textColor="@color/text_primary"
                    android:textSize="24sp"
                    android:textStyle="bold"
                    android:fontFamily="sans-serif-medium"
                    android:layout_gravity="center_horizontal" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="24dp"
                    android:text="Sign in to your account"
                    android:textColor="@color/text_secondary"
                    android:textSize="16sp"
                    android:layout_gravity="center_horizontal" />

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/usernameInputLayout"
                    style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="20dp"
                    android:hint="@string/username_hint"
                    app:startIconDrawable="@drawable/ic_person"
                    app:startIconTint="@color/primary_blue"
                    app:boxCornerRadiusTopStart="12dp"
                    app:boxCornerRadiusTopEnd="12dp"
                    app:boxCornerRadiusBottomStart="12dp"
                    app:boxCornerRadiusBottomEnd="12dp"
                    app:boxStrokeColor="@color/primary_blue"
                    app:hintTextColor="@color/text_secondary">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/editUsername"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="text"
                        android:textColor="@color/text_primary"
                        android:textSize="16sp"
                        android:padding="16dp" />

                </com.google.android.material.textfield.TextInputLayout>

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/passwordInputLayout"
                    style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="32dp"
                    android:hint="@string/password_hint"
                    app:endIconMode="password_toggle"
                    app:endIconTint="@color/primary_blue"
                    app:startIconDrawable="@drawable/ic_lock"
                    app:startIconTint="@color/primary_blue"
                    app:boxCornerRadiusTopStart="12dp"
                    app:boxCornerRadiusTopEnd="12dp"
                    app:boxCornerRadiusBottomStart="12dp"
                    app:boxCornerRadiusBottomEnd="12dp"
                    app:boxStrokeColor="@color/primary_blue"
                    app:hintTextColor="@color/text_secondary">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/editPassword"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="textPassword"
                        android:textColor="@color/text_primary"
                        android:textSize="16sp"
                        android:padding="16dp" />

                </com.google.android.material.textfield.TextInputLayout>

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/buttonLogin"
                    android:layout_width="match_parent"
                    android:layout_height="56dp"
                    android:text="@string/login"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:textColor="@color/text_on_primary"
                    android:backgroundTint="@color/primary_blue"
                    app:cornerRadius="12dp"
                    app:icon="@drawable/ic_login"
                    app:iconTint="@color/text_on_primary"
                    app:iconGravity="textStart"
                    app:elevation="4dp"
                    android:layout_marginTop="8dp" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- Progress Bar -->
        <ProgressBar
            android:id="@+id/progressBar"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="32dp"
            android:visibility="gone"
            android:indeterminateTint="@color/text_on_primary"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/loginCard" />

        <!-- Footer Text -->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="24dp"
            android:layout_marginBottom="32dp"
            android:text="Secure • Reliable • Efficient"
            android:textColor="@color/text_on_primary"
            android:textSize="14sp"
            android:alpha="0.8"
            android:fontFamily="sans-serif"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/progressBar"
            app:layout_constraintBottom_toBottomOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</ScrollView>
