<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res/auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:background="@drawable/subscriber_item_background"
    android:layout_marginBottom="12dp"
    android:padding="20dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical">

        <!-- Avatar Circle -->
        <LinearLayout
            android:layout_width="56dp"
            android:layout_height="56dp"
            android:background="@drawable/circle_gradient_blue"
            android:gravity="center"
            android:layout_marginEnd="16dp">

            <TextView
                android:id="@+id/tvSubscriberInitials"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="AB"
                android:textSize="18sp"
                android:textStyle="bold"
                android:textColor="@color/text_on_primary" />

        </LinearLayout>

        <!-- Subscriber Info -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical">

                <TextView
                    android:id="@+id/textViewSubscriberName"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="Subscriber Name"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="@color/text_primary" />

                <TextView
                    android:id="@+id/tvSubscriberStatus"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Active"
                    android:textSize="12sp"
                    android:textColor="@color/text_on_primary"
                    android:background="@drawable/status_active_background"
                    android:paddingHorizontal="12dp"
                    android:paddingVertical="4dp" />

            </LinearLayout>

            <TextView
                android:id="@+id/textViewSubscriberEmail"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="<EMAIL>"
                android:textSize="14sp"
                android:textColor="@color/text_secondary"
                android:layout_marginTop="4dp"
                android:drawableStart="@drawable/ic_email"
                android:drawablePadding="6dp"
                android:gravity="center_vertical" />

            <TextView
                android:id="@+id/tvSubscriberId"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="ID: 12345"
                android:textSize="12sp"
                android:textColor="@color/text_hint"
                android:layout_marginTop="2dp" />

        </LinearLayout>

        <!-- Action Buttons -->
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginStart="8dp">

            <ImageButton
                android:id="@+id/buttonEditSubscriber"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:src="@drawable/ic_edit"
                android:background="@drawable/action_button_background"
                android:layout_marginEnd="8dp"
                android:contentDescription="Edit Subscriber" />

            <ImageButton
                android:id="@+id/buttonDeleteSubscriber"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:src="@drawable/ic_delete"
                android:background="@drawable/action_button_delete_background"
                android:contentDescription="Delete Subscriber" />

        </LinearLayout>

    </LinearLayout>

</LinearLayout>
