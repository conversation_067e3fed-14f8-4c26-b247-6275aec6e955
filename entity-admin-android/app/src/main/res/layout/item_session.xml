<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res/auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:background="@drawable/session_item_background"
    android:layout_marginBottom="12dp"
    android:padding="20dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical">

        <!-- Session Icon -->
        <ImageView
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:src="@drawable/ic_access_time"
            android:background="@drawable/circle_gradient_blue"
            android:padding="12dp"
            android:layout_marginEnd="16dp" />

        <!-- Session Info -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical">

                <TextView
                    android:id="@+id/textSessionName"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="Session Name"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="@color/text_primary" />

                <TextView
                    android:id="@+id/tvSessionStatus"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Active"
                    android:textSize="12sp"
                    android:textColor="@color/text_on_primary"
                    android:background="@drawable/status_active_background"
                    android:paddingHorizontal="12dp"
                    android:paddingVertical="4dp" />

            </LinearLayout>

            <TextView
                android:id="@+id/tvSessionId"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="ID: 12345"
                android:textSize="14sp"
                android:textColor="@color/text_secondary"
                android:layout_marginTop="4dp" />

            <TextView
                android:id="@+id/tvSessionTime"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Created just now"
                android:textSize="14sp"
                android:textColor="@color/text_secondary"
                android:layout_marginTop="2dp"
                android:drawableStart="@drawable/ic_access_time"
                android:drawablePadding="4dp"
                android:gravity="center_vertical" />

        </LinearLayout>

        <!-- Action Button -->
        <ImageView
            android:id="@+id/ivSessionAction"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:src="@drawable/ic_arrow_forward"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:padding="4dp"
            android:layout_marginStart="8dp" />

    </LinearLayout>

</LinearLayout>
