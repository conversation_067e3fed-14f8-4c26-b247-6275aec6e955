<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fillViewport="true"
    android:background="@color/background_light"
    tools:context=".ui.ReportsFragment">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- Header Section -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="24dp">

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Reports and Analytics"
                    android:textSize="28sp"
                    android:textStyle="bold"
                    android:textColor="@color/text_primary" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Insights and attendance analytics"
                    android:textSize="14sp"
                    android:textColor="@color/text_secondary"
                    android:layout_marginTop="4dp" />

            </LinearLayout>

            <ImageView
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:src="@drawable/ic_assessment"
                android:background="@drawable/circle_gradient_orange"
                android:padding="12dp" />

        </LinearLayout>

        <!-- Time Period Selector -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:background="@drawable/stats_card_background"
            android:padding="16dp"
            android:layout_marginBottom="20dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Time Period"
                android:textSize="16sp"
                android:textStyle="bold"
                android:textColor="@color/text_primary"
                android:layout_marginBottom="12dp" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <Button
                    android:id="@+id/btnWeekly"
                    android:layout_width="0dp"
                    android:layout_height="44dp"
                    android:layout_weight="1"
                    android:layout_marginEnd="4dp"
                    android:text="WEEK"
                    android:textSize="14sp"
                    android:textStyle="bold"
                    android:background="@drawable/button_primary_background"
                    android:textColor="@color/text_button_primary"
                    android:textAllCaps="true"
                    android:elevation="2dp"
                    android:stateListAnimator="@null" />

                <Button
                    android:id="@+id/btnMonthly"
                    android:layout_width="0dp"
                    android:layout_height="44dp"
                    android:layout_weight="1"
                    android:layout_marginHorizontal="4dp"
                    android:text="MONTH"
                    android:textSize="14sp"
                    android:textStyle="bold"
                    android:background="@drawable/button_outline_background"
                    android:textColor="@color/text_button_secondary"
                    android:textAllCaps="true"
                    android:elevation="2dp"
                    android:stateListAnimator="@null" />

                <Button
                    android:id="@+id/btnYearly"
                    android:layout_width="0dp"
                    android:layout_height="44dp"
                    android:layout_weight="1"
                    android:layout_marginStart="4dp"
                    android:text="YEAR"
                    android:textSize="14sp"
                    android:textStyle="bold"
                    android:background="@drawable/button_outline_background"
                    android:textColor="@color/text_button_secondary"
                    android:textAllCaps="true"
                    android:elevation="2dp"
                    android:stateListAnimator="@null" />

            </LinearLayout>

        </LinearLayout>

        <!-- Analytics Cards Grid -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_marginBottom="20dp">

            <!-- First Row -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginBottom="12dp">

                <!-- Total Attendance -->
                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="120dp"
                    android:layout_weight="1"
                    android:layout_marginEnd="8dp"
                    android:orientation="vertical"
                    android:background="@drawable/stats_card_background"
                    android:padding="16dp"
                    android:gravity="center">

                    <ImageView
                        android:layout_width="32dp"
                        android:layout_height="32dp"
                        android:src="@drawable/ic_trending_up"
                        android:background="@drawable/circle_gradient_green"
                        android:padding="8dp"
                        android:layout_marginBottom="8dp" />

                    <TextView
                        android:id="@+id/tvTotalAttendance"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="0"
                        android:textSize="20sp"
                        android:textStyle="bold"
                        android:textColor="@color/success_green" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Total Attendance"
                        android:textSize="10sp"
                        android:textColor="@color/text_secondary"
                        android:textAlignment="center" />

                </LinearLayout>

                <!-- Average Attendance -->
                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="120dp"
                    android:layout_weight="1"
                    android:layout_marginStart="8dp"
                    android:orientation="vertical"
                    android:background="@drawable/stats_card_background"
                    android:padding="16dp"
                    android:gravity="center">

                    <ImageView
                        android:layout_width="32dp"
                        android:layout_height="32dp"
                        android:src="@drawable/ic_assessment"
                        android:background="@drawable/circle_gradient_blue"
                        android:padding="8dp"
                        android:layout_marginBottom="8dp" />

                    <TextView
                        android:id="@+id/tvAverageAttendance"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="0%"
                        android:textSize="20sp"
                        android:textStyle="bold"
                        android:textColor="@color/primary_blue" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Average Rate"
                        android:textSize="10sp"
                        android:textColor="@color/text_secondary"
                        android:textAlignment="center" />

                </LinearLayout>

            </LinearLayout>

            <!-- Second Row -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <!-- Peak Hours -->
                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="120dp"
                    android:layout_weight="1"
                    android:layout_marginEnd="8dp"
                    android:orientation="vertical"
                    android:background="@drawable/stats_card_background"
                    android:padding="16dp"
                    android:gravity="center">

                    <ImageView
                        android:layout_width="32dp"
                        android:layout_height="32dp"
                        android:src="@drawable/ic_access_time"
                        android:background="@drawable/circle_gradient_purple"
                        android:padding="8dp"
                        android:layout_marginBottom="8dp" />

                    <TextView
                        android:id="@+id/tvPeakHours"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="10-11 AM"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        android:textColor="@color/secondary_purple" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Peak Hours"
                        android:textSize="10sp"
                        android:textColor="@color/text_secondary"
                        android:textAlignment="center" />

                </LinearLayout>

                <!-- Most Active Day -->
                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="120dp"
                    android:layout_weight="1"
                    android:layout_marginStart="8dp"
                    android:orientation="vertical"
                    android:background="@drawable/stats_card_background"
                    android:padding="16dp"
                    android:gravity="center">

                    <ImageView
                        android:layout_width="32dp"
                        android:layout_height="32dp"
                        android:src="@drawable/ic_today"
                        android:background="@drawable/circle_gradient_orange"
                        android:padding="8dp"
                        android:layout_marginBottom="8dp" />

                    <TextView
                        android:id="@+id/tvMostActiveDay"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Monday"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        android:textColor="@color/warning_orange" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Most Active"
                        android:textSize="10sp"
                        android:textColor="@color/text_secondary"
                        android:textAlignment="center" />

                </LinearLayout>

            </LinearLayout>

        </LinearLayout>

        <!-- Export Section -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:background="@drawable/stats_card_background"
            android:padding="20dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_marginBottom="16dp">

                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:src="@drawable/ic_download"
                    android:layout_marginEnd="8dp" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Export Reports"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="@color/text_primary" />

            </LinearLayout>

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Download attendance reports in various formats"
                android:textSize="14sp"
                android:textColor="@color/text_secondary"
                android:layout_marginBottom="16dp" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <Button
                    android:id="@+id/btnExportPDF"
                    android:layout_width="0dp"
                    android:layout_height="52dp"
                    android:layout_weight="1"
                    android:layout_marginEnd="8dp"
                    android:text="EXPORT PDF"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:textColor="@color/text_button_primary"
                    android:background="@drawable/button_primary_background"
                    android:gravity="center"
                    android:textAllCaps="true"
                    android:elevation="4dp"
                    android:stateListAnimator="@null" />

                <Button
                    android:id="@+id/btnExportExcel"
                    android:layout_width="0dp"
                    android:layout_height="52dp"
                    android:layout_weight="1"
                    android:layout_marginStart="8dp"
                    android:text="EXPORT EXCEL"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:textColor="@color/text_button_secondary"
                    android:background="@drawable/button_export_outline_background"
                    android:gravity="center"
                    android:textAllCaps="true"
                    android:elevation="4dp"
                    android:stateListAnimator="@null" />

            </LinearLayout>

        </LinearLayout>

    </LinearLayout>
</ScrollView>
