<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fillViewport="true"
    android:background="@color/background_light"
    tools:context=".ui.DashboardFragment">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- Welcome Header Card -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:background="@drawable/gradient_card_background"
            android:padding="24dp"
            android:layout_marginBottom="20dp"
            android:gravity="center_vertical">

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/tvWelcomeMessage"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Welcome Back!"
                    android:textColor="@color/text_on_primary"
                    android:textSize="24sp"
                    android:textStyle="bold" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Here's your attendance overview"
                    android:textColor="@color/text_on_primary"
                    android:textSize="14sp"
                    android:alpha="0.9"
                    android:layout_marginTop="4dp" />

            </LinearLayout>

            <ImageView
                android:layout_width="60dp"
                android:layout_height="60dp"
                android:src="@drawable/ic_dashboard"
                android:alpha="0.8" />

        </LinearLayout>

        <!-- Stats Section Title -->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Quick Stats"
            android:textSize="20sp"
            android:textStyle="bold"
            android:textColor="@color/text_primary"
            android:layout_marginBottom="16dp" />

        <!-- First Row Stats -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="12dp">

            <!-- Total Subscribers Card -->
            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="140dp"
                android:layout_weight="1"
                android:layout_marginEnd="8dp"
                android:orientation="vertical"
                android:background="@drawable/stats_card_background"
                android:padding="20dp"
                android:gravity="center">

                <ImageView
                    android:layout_width="48dp"
                    android:layout_height="48dp"
                    android:src="@drawable/ic_person"
                    android:background="@drawable/circle_gradient_blue"
                    android:padding="12dp"
                    android:layout_marginBottom="12dp" />

                <TextView
                    android:id="@+id/tvTotalSubscribers"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="0"
                    android:textSize="28sp"
                    android:textStyle="bold"
                    android:textColor="@color/primary_blue"
                    android:layout_marginBottom="4dp" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Subscribers"
                    android:textSize="12sp"
                    android:textColor="@color/text_secondary"
                    android:textAlignment="center" />

            </LinearLayout>

            <!-- Total Sessions Card -->
            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="140dp"
                android:layout_weight="1"
                android:layout_marginStart="8dp"
                android:orientation="vertical"
                android:background="@drawable/stats_card_background"
                android:padding="20dp"
                android:gravity="center">

                <ImageView
                    android:layout_width="48dp"
                    android:layout_height="48dp"
                    android:src="@drawable/ic_access_time"
                    android:background="@drawable/circle_gradient_purple"
                    android:padding="12dp"
                    android:layout_marginBottom="12dp" />

                <TextView
                    android:id="@+id/tvTotalSessions"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="0"
                    android:textSize="28sp"
                    android:textStyle="bold"
                    android:textColor="@color/secondary_purple"
                    android:layout_marginBottom="4dp" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Total Sessions"
                    android:textSize="12sp"
                    android:textColor="@color/text_secondary"
                    android:textAlignment="center" />

            </LinearLayout>

        </LinearLayout>

        <!-- Second Row Stats -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="24dp">

            <!-- Today's Sessions Card -->
            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="140dp"
                android:layout_weight="1"
                android:layout_marginEnd="8dp"
                android:orientation="vertical"
                android:background="@drawable/stats_card_background"
                android:padding="20dp"
                android:gravity="center">

                <ImageView
                    android:layout_width="48dp"
                    android:layout_height="48dp"
                    android:src="@drawable/ic_today"
                    android:background="@drawable/circle_gradient_green"
                    android:padding="12dp"
                    android:layout_marginBottom="12dp" />

                <TextView
                    android:id="@+id/tvTodaySessions"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="0"
                    android:textSize="28sp"
                    android:textStyle="bold"
                    android:textColor="@color/success_green"
                    android:layout_marginBottom="4dp" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Today's Sessions"
                    android:textSize="12sp"
                    android:textColor="@color/text_secondary"
                    android:textAlignment="center" />

            </LinearLayout>

            <!-- Active Sessions Card -->
            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="140dp"
                android:layout_weight="1"
                android:layout_marginStart="8dp"
                android:orientation="vertical"
                android:background="@drawable/stats_card_background"
                android:padding="20dp"
                android:gravity="center">

                <ImageView
                    android:layout_width="48dp"
                    android:layout_height="48dp"
                    android:src="@drawable/ic_trending_up"
                    android:background="@drawable/circle_gradient_orange"
                    android:padding="12dp"
                    android:layout_marginBottom="12dp" />

                <TextView
                    android:id="@+id/tvActiveSessions"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="0"
                    android:textSize="28sp"
                    android:textStyle="bold"
                    android:textColor="@color/warning_orange"
                    android:layout_marginBottom="4dp" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Active Sessions"
                    android:textSize="12sp"
                    android:textColor="@color/text_secondary"
                    android:textAlignment="center" />

            </LinearLayout>

        </LinearLayout>

        <!-- Recent Sessions Section -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:background="@drawable/stats_card_background"
            android:padding="20dp"
            android:layout_marginBottom="20dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_marginBottom="16dp">

                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:src="@drawable/ic_access_time"
                    android:layout_marginEnd="8dp" />

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="Recent Sessions"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="@color/text_primary" />

                <TextView
                    android:id="@+id/tvViewAllSessions"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="View All"
                    android:textSize="14sp"
                    android:textColor="@color/primary_blue"
                    android:background="?attr/selectableItemBackgroundBorderless"
                    android:padding="8dp" />

            </LinearLayout>

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recyclerRecentSessions"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:nestedScrollingEnabled="false" />

            <TextView
                android:id="@+id/tvNoRecentSessions"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="No recent sessions found"
                android:textAlignment="center"
                android:textColor="@color/text_secondary"
                android:textSize="16sp"
                android:padding="40dp"
                android:drawableTop="@drawable/ic_access_time"
                android:drawableTint="@color/text_hint"
                android:drawablePadding="16dp"
                android:visibility="gone" />

        </LinearLayout>

        <!-- Quick Actions Section -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:background="@drawable/stats_card_background"
            android:padding="20dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_marginBottom="16dp">

                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:src="@drawable/ic_dashboard"
                    android:layout_marginEnd="8dp" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Quick Actions"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="@color/text_primary" />

            </LinearLayout>

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Manage your attendance system efficiently"
                android:textSize="14sp"
                android:textColor="@color/text_secondary"
                android:layout_marginBottom="20dp" />

            <!-- Action Buttons -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginBottom="12dp">

                <Button
                    android:id="@+id/btnManageSubscribers"
                    android:layout_width="0dp"
                    android:layout_height="60dp"
                    android:layout_weight="1"
                    android:layout_marginEnd="8dp"
                    android:text="Manage\nSubscribers"
                    android:textSize="12sp"
                    android:textColor="@color/text_on_primary"
                    android:background="@drawable/button_primary_background"
                    android:drawableStart="@drawable/ic_person"
                    android:drawableTint="@color/text_on_primary"
                    android:drawablePadding="8dp"
                    android:gravity="center" />

                <Button
                    android:id="@+id/btnViewSessions"
                    android:layout_width="0dp"
                    android:layout_height="60dp"
                    android:layout_weight="1"
                    android:layout_marginStart="8dp"
                    android:text="View\nSessions"
                    android:textSize="12sp"
                    android:textColor="@color/text_on_primary"
                    android:background="@drawable/button_secondary_background"
                    android:drawableStart="@drawable/ic_access_time"
                    android:drawableTint="@color/text_on_primary"
                    android:drawablePadding="8dp"
                    android:gravity="center" />

            </LinearLayout>

            <Button
                android:id="@+id/btnGenerateReports"
                android:layout_width="match_parent"
                android:layout_height="60dp"
                android:text="Generate Reports and Analytics"
                android:textSize="14sp"
                android:textColor="@color/text_on_primary"
                android:textStyle="bold"
                android:background="@drawable/button_primary_background"
                android:drawableStart="@drawable/ic_assessment"
                android:drawableTint="@color/text_on_primary"
                android:drawablePadding="12dp"
                android:gravity="center" />

        </LinearLayout>

    </LinearLayout>
</ScrollView>
