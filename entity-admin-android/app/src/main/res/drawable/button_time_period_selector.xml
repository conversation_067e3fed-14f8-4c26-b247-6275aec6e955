<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- Selected state -->
    <item android:state_selected="true">
        <shape android:shape="rectangle">
            <solid android:color="@color/primary_blue" />
            <corners android:radius="12dp" />
        </shape>
    </item>
    
    <!-- Pressed state -->
    <item android:state_pressed="true">
        <shape android:shape="rectangle">
            <solid android:color="@color/primary_blue_light" />
            <corners android:radius="12dp" />
        </shape>
    </item>
    
    <!-- Default state -->
    <item>
        <shape android:shape="rectangle">
            <solid android:color="@color/surface_white" />
            <corners android:radius="12dp" />
            <stroke
                android:width="2dp"
                android:color="@color/primary_blue" />
        </shape>
    </item>
</selector>
