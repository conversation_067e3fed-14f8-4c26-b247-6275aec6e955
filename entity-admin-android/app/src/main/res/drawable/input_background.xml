<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:state_focused="true">
        <shape>
            <solid android:color="@color/background_white" />
            <corners android:radius="12dp" />
            <stroke
                android:width="2dp"
                android:color="@color/input_border_focused" />
        </shape>
    </item>
    <item>
        <shape>
            <solid android:color="@color/background_white" />
            <corners android:radius="12dp" />
            <stroke
                android:width="1dp"
                android:color="@color/input_border" />
        </shape>
    </item>
</selector>
