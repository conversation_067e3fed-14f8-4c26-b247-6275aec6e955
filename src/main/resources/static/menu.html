<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Restaurant Menu</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f5f5;
            color: #333;
            line-height: 1.6;
        }
        
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            min-height: 100vh;
        }
        
        .header {
            background: #2563eb;
            color: white;
            padding: 1rem;
            position: sticky;
            top: 0;
            z-index: 100;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            font-size: 1.5rem;
            margin-bottom: 0.5rem;
        }
        
        .table-info {
            font-size: 0.9rem;
            opacity: 0.9;
        }
        
        .cart-button {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: #dc2626;
            color: white;
            border: none;
            border-radius: 50px;
            padding: 15px 20px;
            font-size: 1rem;
            font-weight: bold;
            cursor: pointer;
            box-shadow: 0 4px 12px rgba(220, 38, 38, 0.3);
            z-index: 1000;
        }
        
        .cart-count {
            background: white;
            color: #dc2626;
            border-radius: 50%;
            padding: 2px 6px;
            font-size: 0.8rem;
            margin-left: 8px;
        }
        
        .loading {
            text-align: center;
            padding: 2rem;
            font-size: 1.1rem;
            color: #666;
        }
        
        .error {
            background: #fee2e2;
            color: #dc2626;
            padding: 1rem;
            margin: 1rem;
            border-radius: 8px;
            border: 1px solid #fecaca;
        }
        
        .category {
            margin: 1.5rem 0;
        }
        
        .category-title {
            background: #f8fafc;
            padding: 1rem;
            font-size: 1.2rem;
            font-weight: bold;
            color: #1e293b;
            border-left: 4px solid #2563eb;
        }
        
        .menu-item {
            padding: 1rem;
            border-bottom: 1px solid #e2e8f0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .item-info {
            flex: 1;
        }
        
        .item-name {
            font-weight: 600;
            font-size: 1.1rem;
            margin-bottom: 0.25rem;
        }
        
        .item-description {
            color: #64748b;
            font-size: 0.9rem;
            margin-bottom: 0.5rem;
        }
        
        .item-price {
            color: #059669;
            font-weight: bold;
            font-size: 1.1rem;
        }
        
        .item-controls {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .btn {
            border: none;
            border-radius: 6px;
            padding: 8px 12px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .btn-primary {
            background: #2563eb;
            color: white;
        }
        
        .btn-primary:hover {
            background: #1d4ed8;
        }
        
        .btn-outline {
            background: white;
            color: #374151;
            border: 1px solid #d1d5db;
        }
        
        .btn-outline:hover {
            background: #f9fafb;
        }
        
        .quantity {
            font-weight: bold;
            min-width: 30px;
            text-align: center;
        }
        
        .cart-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            z-index: 2000;
            display: none;
        }
        
        .cart-content {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: white;
            border-radius: 16px 16px 0 0;
            max-height: 80vh;
            overflow-y: auto;
        }
        
        .cart-header {
            padding: 1rem;
            border-bottom: 1px solid #e2e8f0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .cart-item {
            padding: 1rem;
            border-bottom: 1px solid #f1f5f9;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .cart-total {
            padding: 1rem;
            background: #f8fafc;
            font-size: 1.2rem;
            font-weight: bold;
            text-align: center;
        }
        
        .checkout-form {
            padding: 1rem;
        }
        
        .form-group {
            margin-bottom: 1rem;
        }
        
        .form-label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
        }
        
        .form-input {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 1rem;
        }
        
        .btn-success {
            background: #059669;
            color: white;
            width: 100%;
            padding: 1rem;
            font-size: 1.1rem;
        }
        
        .btn-success:hover {
            background: #047857;
        }
        
        .btn-success:disabled {
            background: #9ca3af;
            cursor: not-allowed;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Restaurant Menu</h1>
            <div class="table-info" id="tableInfo">
                Loading table information...
            </div>
        </div>
        
        <div id="content">
            <div class="loading">Loading menu...</div>
        </div>
        
        <button class="cart-button" onclick="showCart()">
            🛒 Cart <span class="cart-count" id="cartCount">0</span>
        </button>
    </div>
    
    <!-- Cart Modal -->
    <div class="cart-modal" id="cartModal">
        <div class="cart-content">
            <div class="cart-header">
                <h2>Your Order</h2>
                <button class="btn btn-outline" onclick="hideCart()">Close</button>
            </div>
            <div id="cartItems"></div>
            <div class="cart-total" id="cartTotal">Total: ₹0.00</div>
            <div class="checkout-form">
                <div class="form-group">
                    <label class="form-label">Name *</label>
                    <input type="text" class="form-input" id="customerName" placeholder="Enter your name" required>
                </div>
                <div class="form-group">
                    <label class="form-label">Phone (optional)</label>
                    <input type="tel" class="form-input" id="customerPhone" placeholder="Enter your phone number">
                </div>
                <div class="form-group">
                    <label class="form-label">Special Instructions (optional)</label>
                    <textarea class="form-input" id="orderNotes" placeholder="Any special requests" rows="3"></textarea>
                </div>
                <button class="btn btn-success" onclick="placeOrder()" id="placeOrderBtn">
                    Place Order - ₹0.00
                </button>
            </div>
        </div>
    </div>

    <script>
        // COMPREHENSIVE LOGGING SYSTEM
        console.log('=== MENU PAGE JAVASCRIPT STARTED ===');
        console.log('Current URL:', window.location.href);
        console.log('User Agent:', navigator.userAgent);
        console.log('Timestamp:', new Date().toISOString());

        // Get URL parameters
        const urlParams = new URLSearchParams(window.location.search);
        const entityId = urlParams.get('entityId');
        const tableNumber = urlParams.get('table');
        const qrCode = urlParams.get('qr');

        console.log('=== URL PARAMETERS ===');
        console.log('Entity ID:', entityId);
        console.log('Table Number:', tableNumber);
        console.log('QR Code:', qrCode);
        console.log('All URL Params:', Object.fromEntries(urlParams));
        
        // Cart state
        let cart = [];
        let menuData = [];
        
        // API base URL
        const API_BASE = window.location.origin;
        
        // Load menu data
        async function loadMenu() {
            console.log('=== LOADING MENU DATA ===');
            console.log('API Base URL:', API_BASE);
            console.log('Entity ID for API:', entityId);

            if (!entityId) {
                console.error('ERROR: No entity ID provided!');
                showError('No restaurant ID provided');
                return;
            }

            try {
                const params = new URLSearchParams();
                if (tableNumber) params.append('table', tableNumber);
                if (qrCode) params.append('qr', qrCode);

                const apiUrl = `${API_BASE}/api/public/menu/${entityId}?${params}`;
                console.log('Making API call to:', apiUrl);
                console.log('Request parameters:', Object.fromEntries(params));

                console.log('Fetching menu data...');
                const response = await fetch(apiUrl);
                console.log('Response status:', response.status);
                console.log('Response headers:', Object.fromEntries(response.headers.entries()));

                const data = await response.json();
                console.log('Response data:', data);
                
                if (response.ok) {
                    menuData = data.menu || data;
                    renderMenu();
                    updateTableInfo();
                } else {
                    showError(data.error || 'Failed to load menu');
                }
            } catch (error) {
                console.error('=== MENU LOADING ERROR ===');
                console.error('Error type:', error.name);
                console.error('Error message:', error.message);
                console.error('Error stack:', error.stack);
                console.error('Full error object:', error);

                // Check if it's a network error
                if (error instanceof TypeError && error.message.includes('fetch')) {
                    console.error('NETWORK ERROR: Cannot reach the server');
                    showError('Cannot connect to server. Please check your network connection.');
                } else {
                    showError('Failed to load menu. Please try again.');
                }
            }
        }
        
        function updateTableInfo() {
            const tableInfo = document.getElementById('tableInfo');
            if (tableNumber) {
                tableInfo.textContent = `Table ${tableNumber} • Tap items to add to cart`;
            } else {
                tableInfo.textContent = 'Tap items to add to cart';
            }
        }
        
        function renderMenu() {
            const content = document.getElementById('content');
            
            if (!menuData || menuData.length === 0) {
                content.innerHTML = '<div class="error">No menu items available</div>';
                return;
            }
            
            let html = '';
            menuData.forEach(category => {
                html += `
                    <div class="category">
                        <div class="category-title">${category.name}</div>
                        ${category.items.filter(item => item.isActive).map(item => `
                            <div class="menu-item">
                                <div class="item-info">
                                    <div class="item-name">${item.name}</div>
                                    ${item.description ? `<div class="item-description">${item.description}</div>` : ''}
                                    <div class="item-price">₹${item.price.toFixed(2)}</div>
                                </div>
                                <div class="item-controls">
                                    ${getItemControls(item)}
                                </div>
                            </div>
                        `).join('')}
                    </div>
                `;
            });
            
            content.innerHTML = html;
        }
        
        function getItemControls(item) {
            const cartItem = cart.find(c => c.id === item.id);
            const quantity = cartItem ? cartItem.quantity : 0;
            
            if (quantity === 0) {
                return `<button class="btn btn-primary" onclick="addToCart(${item.id})">Add</button>`;
            } else {
                return `
                    <button class="btn btn-outline" onclick="removeFromCart(${item.id})">-</button>
                    <span class="quantity">${quantity}</span>
                    <button class="btn btn-primary" onclick="addToCart(${item.id})">+</button>
                `;
            }
        }
        
        function addToCart(itemId) {
            const item = findMenuItem(itemId);
            if (!item) return;
            
            const cartItem = cart.find(c => c.id === itemId);
            if (cartItem) {
                cartItem.quantity++;
            } else {
                cart.push({
                    id: item.id,
                    name: item.name,
                    price: item.price,
                    quantity: 1
                });
            }
            
            updateCartDisplay();
            renderMenu(); // Re-render to update controls
        }
        
        function removeFromCart(itemId) {
            const cartItem = cart.find(c => c.id === itemId);
            if (cartItem) {
                cartItem.quantity--;
                if (cartItem.quantity <= 0) {
                    cart = cart.filter(c => c.id !== itemId);
                }
            }
            
            updateCartDisplay();
            renderMenu(); // Re-render to update controls
        }
        
        function findMenuItem(itemId) {
            for (const category of menuData) {
                const item = category.items.find(i => i.id === itemId);
                if (item) return item;
            }
            return null;
        }
        
        function updateCartDisplay() {
            const totalItems = cart.reduce((sum, item) => sum + item.quantity, 0);
            const totalAmount = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
            
            document.getElementById('cartCount').textContent = totalItems;
            document.getElementById('cartTotal').textContent = `Total: ₹${totalAmount.toFixed(2)}`;
            document.getElementById('placeOrderBtn').textContent = `Place Order - ₹${totalAmount.toFixed(2)}`;
        }
        
        function showCart() {
            renderCartItems();
            document.getElementById('cartModal').style.display = 'block';
        }
        
        function hideCart() {
            document.getElementById('cartModal').style.display = 'none';
        }
        
        function renderCartItems() {
            const cartItems = document.getElementById('cartItems');
            
            if (cart.length === 0) {
                cartItems.innerHTML = '<div style="padding: 2rem; text-align: center; color: #666;">Your cart is empty</div>';
                return;
            }
            
            let html = '';
            cart.forEach(item => {
                html += `
                    <div class="cart-item">
                        <div>
                            <div style="font-weight: 600;">${item.name}</div>
                            <div style="color: #666;">₹${item.price.toFixed(2)} each</div>
                        </div>
                        <div class="item-controls">
                            <button class="btn btn-outline" onclick="removeFromCart(${item.id})">-</button>
                            <span class="quantity">${item.quantity}</span>
                            <button class="btn btn-primary" onclick="addToCart(${item.id})">+</button>
                        </div>
                    </div>
                `;
            });
            
            cartItems.innerHTML = html;
        }
        
        async function placeOrder() {
            if (cart.length === 0) {
                alert('Please add items to your cart');
                return;
            }
            
            const customerName = document.getElementById('customerName').value.trim();
            if (!customerName) {
                alert('Please enter your name');
                return;
            }
            
            const orderBtn = document.getElementById('placeOrderBtn');
            orderBtn.disabled = true;
            orderBtn.textContent = 'Placing Order...';
            
            try {
                const orderData = {
                    customerName: customerName,
                    customerPhone: document.getElementById('customerPhone').value.trim() || null,
                    notes: document.getElementById('orderNotes').value.trim() || null,
                    tableNumber: tableNumber ? parseInt(tableNumber) : null,
                    orderItems: cart.map(item => ({
                        itemId: item.id,
                        quantity: item.quantity,
                        price: item.price
                    }))
                };
                
                const params = new URLSearchParams();
                if (tableNumber) params.append('table', tableNumber);
                if (qrCode) params.append('qr', qrCode);
                
                const response = await fetch(`${API_BASE}/api/public/menu/${entityId}/order?${params}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(orderData)
                });
                
                const result = await response.json();
                
                if (response.ok) {
                    alert(`Order placed successfully! Order number: ${result.orderNumber}`);
                    cart = [];
                    updateCartDisplay();
                    hideCart();
                    renderMenu();
                } else {
                    alert(result.error || 'Failed to place order');
                }
            } catch (error) {
                console.error('Error placing order:', error);
                alert('Failed to place order. Please try again.');
            } finally {
                orderBtn.disabled = false;
                orderBtn.textContent = `Place Order - ₹${cart.reduce((sum, item) => sum + (item.price * item.quantity), 0).toFixed(2)}`;
            }
        }
        
        function showError(message) {
            document.getElementById('content').innerHTML = `<div class="error">${message}</div>`;
        }
        
        // Network connectivity test
        async function testNetworkConnectivity() {
            console.log('=== TESTING NETWORK CONNECTIVITY ===');
            try {
                const testUrl = `${API_BASE}/api/public/menu/test-connectivity`;
                console.log('Testing connectivity to:', testUrl);
                const response = await fetch(testUrl, { method: 'GET', timeout: 5000 });
                console.log('Connectivity test response:', response.status);
                return true;
            } catch (error) {
                console.error('Connectivity test failed:', error);
                return false;
            }
        }

        // Initialize with comprehensive logging
        async function initialize() {
            console.log('=== INITIALIZING MENU APPLICATION ===');
            console.log('DOM loaded:', document.readyState);
            console.log('Window loaded:', document.readyState === 'complete');

            // Test network connectivity first
            const isConnected = await testNetworkConnectivity();
            console.log('Network connectivity:', isConnected ? 'OK' : 'FAILED');

            // Load menu data
            console.log('Starting menu load...');
            await loadMenu();
            console.log('Menu load completed');
        }

        // Start initialization
        console.log('=== STARTING APPLICATION ===');
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', initialize);
            console.log('Waiting for DOM to load...');
        } else {
            initialize();
            console.log('DOM already loaded, initializing immediately');
        }
        
        // Close cart when clicking outside
        document.getElementById('cartModal').addEventListener('click', function(e) {
            if (e.target === this) {
                hideCart();
            }
        });
    </script>
</body>
</html>
