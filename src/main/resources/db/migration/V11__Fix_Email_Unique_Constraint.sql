-- Fix email unique constraint issue for subscribers
-- Remove the unique constraint on email field since email is optional

-- Step 1: Check current constraints on subscribers table
-- This will help us identify the exact constraint name

-- Step 2: Drop the unique constraint on email
-- The constraint name might be auto-generated by Hibernate
DO $$
DECLARE
    constraint_name_var TEXT;
BEGIN
    -- Find the unique constraint on email column
    SELECT constraint_name INTO constraint_name_var
    FROM information_schema.table_constraints tc
    JOIN information_schema.constraint_column_usage ccu ON tc.constraint_name = ccu.constraint_name
    WHERE tc.table_name = 'subscribers' 
    AND tc.constraint_type = 'UNIQUE'
    AND ccu.column_name = 'email';
    
    -- Drop the constraint if it exists
    IF constraint_name_var IS NOT NULL THEN
        EXECUTE 'ALTER TABLE subscribers DROP CONSTRAINT ' || constraint_name_var;
        RAISE NOTICE 'Dropped unique constraint on email: %', constraint_name_var;
    ELSE
        RAISE NOTICE 'No unique constraint found on email column';
    END IF;
    
EXCEPTION
    WHEN OTHERS THEN
        RAISE NOTICE 'Error removing email unique constraint: %', SQLERRM;
END $$;

-- Step 3: Ensure email column allows NULL values
ALTER TABLE subscribers ALTER COLUMN email DROP NOT NULL;

-- Step 4: Clean up any empty string emails to NULL
UPDATE subscribers SET email = NULL WHERE email = '' OR email IS NULL;

-- Step 5: Verify the fix
SELECT 
    'Email Constraint Check' as check_type,
    COUNT(*) as total_subscribers,
    COUNT(email) as subscribers_with_email,
    COUNT(CASE WHEN email = '' THEN 1 END) as subscribers_with_empty_email
FROM subscribers;

-- Step 6: Show current constraints on subscribers table
SELECT 
    'Current Constraints' as info,
    constraint_name,
    constraint_type,
    column_name
FROM information_schema.table_constraints tc
JOIN information_schema.constraint_column_usage ccu ON tc.constraint_name = ccu.constraint_name
WHERE tc.table_name = 'subscribers'
ORDER BY constraint_type, constraint_name;
