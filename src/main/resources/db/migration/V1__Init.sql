CREATE TABLE IF NOT EXISTS roles (
    id SERIAL PRIMARY KEY,
    name <PERSON><PERSON><PERSON><PERSON>(50) UNIQUE NOT NULL
);

CREATE TABLE IF NOT EXISTS organizations (
    id SERIAL PRIMARY KEY,
    name VA<PERSON><PERSON><PERSON>(255) NOT NULL
);

CREATE TABLE IF NOT EXISTS entity_admins (
    id SERIAL PRIMARY KEY,
    username VA<PERSON><PERSON><PERSON>(255) NOT NULL,
    password VARCHAR(255) NOT NULL,
    organization_id INTEGER NOT NULL REFERENCES organizations(id),
    role_id INTEGER REFERENCES roles(id)
);
