# PostgreSQL Database Configuration
spring.datasource.url=${J<PERSON><PERSON>_DATABASE_URL:**********************************************}
spring.datasource.username=${JD<PERSON>_DATABASE_USERNAME:postgres}
spring.datasource.password=${JD<PERSON>_DATABASE_PASSWORD:0000}
spring.datasource.driver-class-name=org.postgresql.Driver

# JPA Configuration
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=false
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.PostgreSQLDialect
spring.jpa.properties.hibernate.format_sql=false

# Flyway settings
spring.flyway.enabled=false
spring.flyway.locations=classpath:db/migration
spring.flyway.baseline-on-migrate=true

# JWT Configuration
jwt.secret=mySecretKey
jwt.expiration=86400000

# Server Configuration - MOBILE ACCESS FIX
server.port=8080
server.address=0.0.0.0

# Application URL Configuration
app.base-url=${APP_BASE_URL:http://localhost:3002}

# Logging - COMPREHENSIVE DEBUG MODE
logging.level.com.example.attendancesystem=DEBUG
logging.level.org.springframework.security=WARN
logging.level.org.springframework.web=DEBUG
logging.level.org.springframework.web.servlet.DispatcherServlet=DEBUG
logging.level.org.springframework.boot.web.servlet.support.ErrorPageFilter=DEBUG
logging.level.org.springframework.web.servlet.resource=DEBUG
logging.level.org.springframework.web.servlet.mvc=DEBUG

# Jackson Configuration for LocalDateTime
spring.jackson.serialization.write-dates-as-timestamps=false
spring.jackson.time-zone=Asia/Kolkata
spring.jackson.date-format=yyyy-MM-dd HH:mm:ss

# mDNS Service Discovery Configuration
app.mdns.enabled=true
app.mdns.service-name=RestaurantSystem
app.mdns.hostname=restaurant.local

# Face Recognition Configuration (SeetaFace6)
face.recognition.models.detector=src/main/resources/native/models/face_detector.csta
face.recognition.models.landmark=src/main/resources/native/models/face_landmarker_pts68.csta
face.recognition.models.recognizer=src/main/resources/native/models/face_recognizer.csta
face.recognition.models.antispoofing=src/main/resources/native/models/fas_first.csta
face.recognition.fallback.enabled=true

# File Upload Configuration
spring.servlet.multipart.max-file-size=10MB
spring.servlet.multipart.max-request-size=10MB

# Face Recognition Storage
face.recognition.storage.path=uploads/faces/
face.recognition.storage.profile-photos=uploads/profiles/
